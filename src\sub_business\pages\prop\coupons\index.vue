<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" v-model="pageData" @query="queryList" :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="优惠卷"></CustomNavBar>
    </template>
    <view class="coupons-box">
      <!-- 分类标题 -->
      <view class="section-title">未使用</view>

      <!-- 未使用优惠券列表 -->
      <view class="coupons-list">
        <view v-for="(coupon, index) in unusedCoupons" :key="index" class="coupon-card">
          <view class="coupon-main">
            <view class="coupon-left">
              <!-- 优惠券金额 -->
              <view class="coupon-amount">
                <text class="currency">¥</text>
                <text class="amount">{{ coupon.amount }}</text>
              </view>
            </view>

            <view class="coupon-divider"></view>

            <view class="coupon-right">
              <!-- 优惠券信息 -->
              <view class="coupon-info">
                <view class="coupon-title">{{ coupon.title }}</view>
                <view class="coupon-desc">{{ coupon.description }}</view>
              </view>

              <!-- 使用按钮 -->
              <view class="coupon-action">
                <wd-button size="small" custom-class="use-btn" @click="useCoupon(coupon)">
                  立即使用
                </wd-button>
              </view>
            </view>
          </view>
          <!-- 在有效期上方插入横向分割线 -->
          <view class="coupon-horizontal-divider"></view>
          <view class="coupon-validity">有效期至{{ coupon.validUntil }}</view>
        </view>
      </view>

      <!-- 已使用分类 -->
      <view class="section-title">已使用</view>
      <view class="coupons-list">
        <view v-for="(coupon, index) in usedCoupons" :key="index" class="coupon-card used-card">
          <view class="coupon-main">
            <view class="coupon-left">
              <view class="coupon-amount">
                <text class="currency">¥</text>
                <text class="amount">{{ coupon.amount }}</text>
              </view>
            </view>

            <view class="coupon-divider"></view>

            <view class="coupon-right">
              <view class="coupon-info">
                <view class="coupon-title">{{ coupon.title }}</view>
                <view class="coupon-desc">{{ coupon.description }}</view>
              </view>

              <view class="coupon-action">
                <view class="status-text">离位·电商管理</view>
              </view>
            </view>
          </view>
          <!-- 在有效期上方插入横向分割线 -->
          <view class="coupon-horizontal-divider"></view>
          <view class="coupon-validity">使用时间{{ coupon.usedTime }}</view>
        </view>
      </view>

      <!-- 已过期分类 -->
      <view class="section-title">已过期</view>
      <view class="coupons-list">
        <view
          v-for="(coupon, index) in expiredCoupons"
          :key="index"
          class="coupon-card expired-card"
        >
          <view class="coupon-main">
            <view class="coupon-left">
              <view class="coupon-amount">
                <text class="currency">¥</text>
                <text class="amount">{{ coupon.amount }}</text>
              </view>
            </view>

            <view class="coupon-divider"></view>

            <view class="coupon-right">
              <view class="coupon-info">
                <view class="coupon-title">{{ coupon.title }}</view>
                <view class="coupon-desc">{{ coupon.description }}</view>
              </view>

              <view class="coupon-action">
                <view class="status-text">已过期</view>
              </view>
            </view>
          </view>
          <!-- 在有效期上方插入横向分割线 -->
          <view class="coupon-horizontal-divider"></view>
          <view class="coupon-validity">有效期至{{ coupon.validUntil }}</view>
        </view>
      </view>
    </view>
  </z-paging>
  <wd-toast />
  <wd-message-box />
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 页面样式配置
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 模拟优惠券数据
const couponsData = ref([
  {
    id: 1,
    amount: 52,
    title: '神马作弊卡-抵扣券',
    description: '满500减52',
    validUntil: '2023.05.09 23:59:59',
    status: 'unused',
  },
  {
    id: 2,
    amount: 52,
    title: '神马作弊卡-抵扣券',
    description: '无门槛使用',
    validUntil: '2023.05.09 23:59:59',
    status: 'unused',
  },
  {
    id: 3,
    amount: 8.5,
    title: '神马作弊卡-折扣券',
    description: '无门槛折扣',
    validUntil: '2023.05.09 23:59:59',
    status: 'unused',
  },
  {
    id: 4,
    amount: 52,
    title: '神马作弊卡-抵扣券',
    description: '无门槛使用',
    validUntil: '2023.05.09 23:59:59',
    usedTime: '2023.05.09 23:59:59',
    status: 'used',
  },
  {
    id: 5,
    amount: 30,
    title: '神马作弊卡-抵扣券',
    description: '满200使用',
    validUntil: '2023.04.15 23:59:59',
    status: 'expired',
  },
  {
    id: 6,
    amount: 15,
    title: '神马作弊卡-折扣券',
    description: '无门槛折扣',
    validUntil: '2023.03.20 23:59:59',
    status: 'expired',
  },
])

const queryList = async (page: number, size: number) => {
  pageSetInfo(page, size)
  pagingRef.value.complete(couponsData.value)
}

// 未使用的优惠券
const unusedCoupons = computed(() => {
  return couponsData.value.filter((coupon) => coupon.status === 'unused')
})

// 已使用的优惠券
const usedCoupons = computed(() => {
  return couponsData.value.filter((coupon) => coupon.status === 'used')
})

// 已过期的优惠券
const expiredCoupons = computed(() => {
  return couponsData.value.filter((coupon) => coupon.status === 'expired')
})

// 使用优惠券
const useCoupon = (coupon: any) => {
  // 这里可以调用使用优惠券的API
  console.log('使用优惠券:', coupon)
  // 模拟使用成功后更新状态
  coupon.status = 'used'
  coupon.usedTime = '2023.05.09 23:59:59'
}
</script>

<style scoped lang="scss">
.coupons-box {
  padding: 0rpx 40rpx;
}

// 分类标题样式
.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin: 40rpx 0 30rpx 0;

  &:first-child {
    margin-top: 20rpx;
  }
}

// 优惠券列表样式
.coupons-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

// 优惠券卡片样式
.coupon-card {
  position: relative;
  background: #ffffff;
  border-radius: 32rpx;
  overflow: visible;
  margin-bottom: 20rpx;
  display: block;
  box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(0, 0, 0, 0.1);

  &.used-card,
  &.expired-card {
    .coupon-main {
      opacity: 0.6;
    }

    .coupon-amount {
      color: #999999;
    }

    .coupon-info {
      .coupon-title,
      .coupon-desc {
        color: #999999;
      }
    }
  }
}

// 优惠券主体部分
.coupon-main {
  display: flex;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  position: relative;
  min-height: 160rpx;
  overflow: hidden;
}

// 优惠券左侧（金额区域）
.coupon-left {
  width: 160rpx;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 20rpx 0;
}

// 优惠券金额样式
.coupon-amount {
  display: flex;
  align-items: baseline;
  color: #ff4545;

  .currency {
    font-size: 24rpx;
    font-weight: 500;
  }

  .amount {
    font-size: 48rpx;
    font-weight: bold;
    margin-left: 4rpx;
  }
}

// 分割线（虚线效果）
.coupon-divider {
  width: 2rpx;
  background: repeating-linear-gradient(
    to bottom,
    #ff2b75 0rpx,
    #ff2b75 8rpx,
    transparent 8rpx,
    transparent 16rpx
  );
  position: relative;
  margin: 0 10rpx;

  &::before {
    top: -8rpx;
  }

  &::after {
    bottom: -8rpx;
  }
}

// 优惠券右侧（信息区域）
.coupon-right {
  flex: 1;
  padding: 20rpx 30rpx 20rpx 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

// 优惠券信息样式
.coupon-info {
  flex: 1;

  .coupon-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #ff4545;
    line-height: 1.3;
    margin-bottom: 6rpx;
  }

  .coupon-desc {
    font-size: 22rpx;
    color: #666666;
    line-height: 1.2;
  }
}

// 优惠券操作区域样式
.coupon-action {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 15rpx;
}

// 使用按钮样式
:deep(.use-btn) {
  background: #ff4545 !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 25rpx !important;
  padding: 8rpx 24rpx !important;
  font-size: 22rpx !important;
  font-weight: 500 !important;
  min-width: 120rpx !important;
  height: 50rpx !important;
}

// 状态文字样式
.status-text {
  font-size: 20rpx;
  padding: 8rpx 20rpx;
  border-radius: 32rpx;
  text-align: center;
  min-width: 120rpx;
  background: #f0f0f0;
  color: #999999;
  height: 50rpx;
  line-height: 34rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 有效期样式
.coupon-validity {
  background: rgba(255, 69, 69, 0.1);
  min-height: 60rpx;
  padding: 15rpx 20rpx;
  font-size: 22rpx;
  color: #999999;
  border-radius: 0 0 32rpx 32rpx;
  text-align: left;
  display: flex;
  align-items: center;
}

.coupon-horizontal-divider {
  height: 2rpx;
  background: repeating-linear-gradient(
    to right,
    #ff2b75 0rpx,
    #ff2b75 8rpx,
    transparent 8rpx,
    transparent 16rpx
  );
  position: relative;
  margin: 0 32rpx;

  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 32rpx;
    height: 32rpx;
    background: linear-gradient(125deg, #ffdede 0%, #ebeffa 20%, #ffffff 100%);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
  }

  &::before {
    left: -80rpx;
  }

  &::after {
    right: -80rpx;
  }
}
</style>
